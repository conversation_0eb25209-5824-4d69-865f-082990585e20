version: "3.9"

services:
  # API Service Instances
  app1:
    build: .
    env_file: .env
    deploy:
      resources:
        limits:
          cpus: "1.0"
          memory: 4g
    expose:
      - "3000"
    restart: always
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000/healthz"]
      interval: 15s
      timeout: 5s
      retries: 2
      start_period: 20s
    environment:
      - NODE_ENV=production
      - PORT=3000
      - INSTANCE_ID=1
      # Database connection settings
      - PGHOST=postgres
      - PGPORT=5432
      - PGDATABASE=${PGDATABASE}
      - PGUSER=${PGUSER}
      - PGPASSWORD=${PGPASSWORD}
      - PGSSLMODE=disable
      # Database pool optimization
      - PG_MAX_CONNECTIONS=20
      - PG_MIN_CONNECTIONS=5
      - PG_IDLE_TIMEOUT=30000
      # Redis connection
      - REDIS_ENABLED=true
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - REDIS_DEFAULT_TTL=7200
      # Performance optimizations
      - CONNECTION_WARMUP_INTERVAL=30000
      - MEMORY_CACHE_SIZE=500
      - MAX_MEMORY_CACHE_ITEMS=2000
      # API settings
      - API_URL=${API_URL}
      - INTERNAL_API_KEY=${INTERNAL_API_KEY}
      - ENCRYPTION_KEY=${ENCRYPTION_KEY}
      - RAPIDAPI_PROXY_SECRET=${RAPIDAPI_PROXY_SECRET}
    volumes:
      - type: bind
        source: ./logs
        target: /app/logs
    depends_on:
      - postgres
      - redis
    networks:
      - tempmail-network

  # PostgreSQL Database with pg_cron
  postgres:
    build:
      context: .
      dockerfile: postgres.Dockerfile
    deploy:
      resources:
        limits:
          cpus: "2.0"
          memory: 4g
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./pg_init:/docker-entrypoint-initdb.d:ro
    environment:
      - POSTGRES_USER=${PGUSER}
      - POSTGRES_PASSWORD=${PGPASSWORD}
      - POSTGRES_DB=${PGDATABASE}
    command: >
      postgres
        -c shared_buffers=2GB
        -c effective_cache_size=4GB
        -c maintenance_work_mem=512MB
        -c work_mem=16MB
        -c max_connections=200
        -c max_worker_processes=6
        -c max_parallel_workers=6
        -c max_parallel_workers_per_gather=3
        -c random_page_cost=1.1
        -c effective_io_concurrency=200
        -c checkpoint_completion_target=0.9
        -c wal_buffers=16MB
        -c default_statistics_target=100
        -c autovacuum=on
        -c shared_preload_libraries=pg_cron
        -c cron.database_name=${PGDATABASE}
    restart: always
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${PGUSER} -d ${PGDATABASE}"]
      interval: 30s
      timeout: 5s
      retries: 3
      start_period: 30s
    networks:
      - tempmail-network

  # Redis Cache
  redis:
    image: redis:7-alpine
    deploy:
      resources:
        limits:
          cpus: "1.0"
          memory: 2g
    volumes:
      - redis_data:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf
    command: ["redis-server", "/usr/local/etc/redis/redis.conf", "--requirepass", "${REDIS_PASSWORD}"]
    restart: always
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "ping"]
      interval: 30s
      timeout: 5s
      retries: 3
      start_period: 10s
    networks:
      - tempmail-network


volumes:
  postgres_data:
  redis_data:

networks:
  tempmail-network:
    driver: bridge