#!/usr/bin/env node

/**
 * <PERSON>ript to apply performance optimizations to the TempFly.io database
 * This script creates optimized indexes for the inbox count queries
 */

const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');

// Database configuration
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '5432'),
  database: process.env.DB_NAME || 'tempmail',
  user: process.env.DB_USER || 'tempmail_user',
  password: process.env.DB_PASSWORD || 'your_password_here',
  ssl: process.env.DB_SSL === 'true' ? { rejectUnauthorized: false } : false
};

async function applyOptimizations() {
  console.log('🚀 Applying performance optimizations to TempFly.io database...');
  
  const pool = new Pool(dbConfig);
  
  try {
    // Test connection
    const client = await pool.connect();
    console.log('✅ Connected to database successfully');
    
    // Read the optimization SQL file
    const sqlFile = path.join(__dirname, 'pg_init', 'optimize-inbox-count-query.sql');
    const sql = fs.readFileSync(sqlFile, 'utf8');
    
    console.log('📝 Executing optimization queries...');
    
    // Split SQL into individual statements and execute them
    const statements = sql.split(';').filter(stmt => stmt.trim().length > 0);
    
    for (const statement of statements) {
      const trimmedStatement = statement.trim();
      if (trimmedStatement && !trimmedStatement.startsWith('--')) {
        try {
          console.log(`   Executing: ${trimmedStatement.substring(0, 50)}...`);
          await client.query(trimmedStatement);
          console.log('   ✅ Success');
        } catch (error) {
          if (error.message.includes('already exists')) {
            console.log('   ⚠️  Index already exists, skipping');
          } else {
            console.error(`   ❌ Error: ${error.message}`);
          }
        }
      }
    }
    
    // Verify the optimizations
    console.log('\n🔍 Verifying optimizations...');
    
    // Check if the new indexes exist
    const indexQuery = `
      SELECT indexname, tablename 
      FROM pg_indexes 
      WHERE tablename = 'inboxes' 
      AND indexname LIKE 'idx_inboxes_rapidapi%'
      ORDER BY indexname;
    `;
    
    const result = await client.query(indexQuery);
    console.log('\n📊 Inbox-related indexes:');
    result.rows.forEach(row => {
      console.log(`   ✅ ${row.indexname} on ${row.tablename}`);
    });
    
    // Test query performance
    console.log('\n⚡ Testing query performance...');
    const testQuery = `
      EXPLAIN (ANALYZE, BUFFERS) 
      SELECT COUNT(*) FROM inboxes 
      WHERE rapidapi_key = 'test-key' 
      AND is_active = true 
      AND (expiry_date IS NULL OR expiry_date > NOW());
    `;
    
    const explainResult = await client.query(testQuery);
    console.log('\n📈 Query execution plan:');
    explainResult.rows.forEach(row => {
      console.log(`   ${row['QUERY PLAN']}`);
    });
    
    client.release();
    console.log('\n🎉 Performance optimizations applied successfully!');
    
    console.log('\n💡 Recommendations:');
    console.log('   - Monitor query performance with the new indexes');
    console.log('   - Run ANALYZE on the inboxes table periodically');
    console.log('   - Consider adding more specific indexes if needed');
    
  } catch (error) {
    console.error('❌ Failed to apply optimizations:', error.message);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

/**
 * Test the optimized query performance
 */
async function testQueryPerformance() {
  console.log('\n🧪 Testing optimized query performance...');
  
  const pool = new Pool(dbConfig);
  
  try {
    const client = await pool.connect();
    
    // Test with a real rapidapi_key if available
    const testKeys = ['080d9470b9msh2f8d8d677bd8c80p1a6bc2jsna4dd35114f11', 'test-key'];
    
    for (const testKey of testKeys) {
      console.log(`\n🔍 Testing with key: ${testKey.substring(0, 20)}...`);
      
      const startTime = Date.now();
      const result = await client.query(`
        SELECT COUNT(*) as count FROM inboxes 
        WHERE rapidapi_key = $1 
        AND is_active = true 
        AND (expiry_date IS NULL OR expiry_date > NOW())
      `, [testKey]);
      
      const duration = Date.now() - startTime;
      const count = result.rows[0].count;
      
      console.log(`   📊 Found ${count} active inboxes in ${duration}ms`);
      
      if (duration > 100) {
        console.log(`   ⚠️  Query took ${duration}ms - consider further optimization`);
      } else {
        console.log(`   ✅ Query performance is good (${duration}ms)`);
      }
    }
    
    client.release();
  } catch (error) {
    console.error('❌ Performance test failed:', error.message);
  } finally {
    await pool.end();
  }
}

// Main execution
async function main() {
  console.log('🔧 TempFly.io Performance Optimization Tool\n');
  
  const args = process.argv.slice(2);
  
  if (args.includes('--test-only')) {
    await testQueryPerformance();
  } else {
    await applyOptimizations();
    await testQueryPerformance();
  }
  
  console.log('\n✨ Performance optimization complete!');
}

// Run the script
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  applyOptimizations,
  testQueryPerformance
};
