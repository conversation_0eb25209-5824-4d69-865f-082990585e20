/**
 * Database Migration Script for TempFly.io
 * 
 * This script can be run manually to initialize or update the database schema.
 * It reads the SQL file and executes it against the PostgreSQL database.
 */

require('dotenv').config();
const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');

// Create a connection pool
const pool = new Pool({
  host: process.env.PGHOST || 'postgres',
  port: parseInt(process.env.PGPORT || '5432'),
  database: process.env.PGDATABASE || 'tempmail',
  user: process.env.PGUSER || 'tempmail_user',
  password: process.env.PGPASSWORD,
  ssl: process.env.PGSSLMODE === 'disable' ? false : {
    rejectUnauthorized: false
  }
});

async function runMigration() {
  console.log('Starting database migration...');
  
  try {
    // Read the SQL file
    const sqlFilePath = path.join(__dirname, 'pg_init', 'create-tables.sql');
    const sql = fs.readFileSync(sqlFilePath, 'utf8');
    
    // Connect to the database
    const client = await pool.connect();
    
    try {
      // Begin transaction
      await client.query('BEGIN');
      
      console.log('Executing SQL script...');
      // Execute the SQL script
      await client.query(sql);
      
      // Commit transaction
      await client.query('COMMIT');
      
      console.log('Database migration completed successfully!');
      
      // Check which tables were created
      const tablesResult = await client.query(
        "SELECT table_name FROM information_schema.tables WHERE table_schema='public'"
      );
      
      console.log('Created tables:');
      tablesResult.rows.forEach(row => {
        console.log(`- ${row.table_name}`);
      });
      
    } catch (err) {
      // Rollback transaction on error
      await client.query('ROLLBACK');
      throw err;
    } finally {
      // Release the client back to the pool
      client.release();
    }
  } catch (err) {
    console.error('Error during migration:', err);
    process.exit(1);
  } finally {
    // Close the pool
    await pool.end();
  }
}

// Run the migration
runMigration();
