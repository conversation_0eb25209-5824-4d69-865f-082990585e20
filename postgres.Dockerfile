FROM postgres:15-alpine

# Install build dependencies
RUN apk add --no-cache --virtual .build-deps \
    git \
    make \
    gcc \
    libc-dev \
    clang \
    llvm \
    postgresql-dev \
    curl \
    ca-certificates \
    openssl \
    tar

# Set PostgreSQL version for pg_config path
ENV PG_MAJOR=15

# Clone a specific release tag of pg_cron for better stability
RUN cd /tmp && \
    git clone --branch v1.5.2 --depth 1 https://github.com/citusdata/pg_cron.git && \
    cd pg_cron && \
    # Fix all clang references in PostgreSQL PGXS system files
    find /usr/local/lib/postgresql/pgxs -name "*.mk" -exec sed -i 's/clang-[0-9]*/clang/g' {} \; 2>/dev/null || true && \
    find /usr/local/lib/postgresql/pgxs -name "Makefile.global" -exec sed -i 's/clang-[0-9]*/clang/g' {} \; 2>/dev/null || true && \
    # Fix the main PostgreSQL Makefile.global that's causing the issue
    sed -i 's/clang-[0-9]*/clang/g' /usr/local/lib/postgresql/pgxs/src/makefiles/../../src/Makefile.global 2>/dev/null || true && \
    # Completely disable LLVM/bitcode compilation in PGXS makefiles
    sed -i 's/with_llvm = yes/with_llvm = no/g' /usr/local/lib/postgresql/pgxs/src/makefiles/pgxs.mk 2>/dev/null || true && \
    sed -i '/install.*bitcode/d' /usr/local/lib/postgresql/pgxs/src/makefiles/pgxs.mk 2>/dev/null || true && \
    sed -i '/llvm-lto/d' /usr/local/lib/postgresql/pgxs/src/makefiles/pgxs.mk 2>/dev/null || true && \
    # Fix all clang references in pg_cron Makefile and disable LLVM completely
    sed -i 's/clang-[0-9]*/clang/g' Makefile && \
    sed -i 's/clang-19/clang/g' Makefile && \
    # Completely disable LLVM compilation by removing the bytecode targets
    sed -i '/\.bc:/d' Makefile && \
    sed -i '/emit-llvm/d' Makefile && \
    sed -i '/bitcode/d' Makefile && \
    # Build with explicit settings to avoid LLVM
    make CC=gcc LLVM_CONFIG= with_llvm=no && \
    make install CC=gcc LLVM_CONFIG= with_llvm=no

# Cleanup build dependencies to reduce image size
RUN apk del .build-deps

# Copy initialization scripts
COPY pg_init /docker-entrypoint-initdb.d/

# Set environment variables for PostgreSQL
ENV POSTGRES_HOST_AUTH_METHOD=trust

# Expose PostgreSQL port
EXPOSE 5432

# Use the default entrypoint from the postgres image
CMD ["postgres", \
     "-c", "shared_buffers=2GB", \
     "-c", "effective_cache_size=4GB", \
     "-c", "maintenance_work_mem=512MB", \
     "-c", "work_mem=16MB", \
     "-c", "max_connections=200", \
     "-c", "max_worker_processes=6", \
     "-c", "max_parallel_workers=6", \
     "-c", "max_parallel_workers_per_gather=3", \
     "-c", "random_page_cost=1.1", \
     "-c", "effective_io_concurrency=200", \
     "-c", "checkpoint_completion_target=0.9", \
     "-c", "wal_buffers=16MB", \
     "-c", "default_statistics_target=100", \
     "-c", "autovacuum=on", \
     "-c", "shared_preload_libraries=pg_cron", \
     "-c", "cron.database_name=tempmail"]
