#!/bin/bash

# TempFly.io Timeout Fix Deployment Script
# This script applies all performance optimizations to fix the timeout issue

set -e  # Exit on any error

echo "🚀 Deploying TempFly.io Timeout Fix..."
echo "======================================"

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    echo "❌ Error: package.json not found. Please run this script from the project root."
    exit 1
fi

# Check if Node.js is available
if ! command -v node &> /dev/null; then
    echo "❌ Error: Node.js is not installed or not in PATH."
    exit 1
fi

# Step 1: Apply database optimizations
echo ""
echo "📊 Step 1: Applying database optimizations..."
if [ -f "apply-performance-optimizations.js" ]; then
    echo "   Running database optimization script..."
    node apply-performance-optimizations.js
    echo "   ✅ Database optimizations applied"
else
    echo "   ⚠️  Database optimization script not found, skipping..."
fi

# Step 2: Install any new dependencies (if needed)
echo ""
echo "📦 Step 2: Checking dependencies..."
if [ -f "package-lock.json" ]; then
    echo "   Running npm ci..."
    npm ci
elif [ -f "yarn.lock" ]; then
    echo "   Running yarn install..."
    yarn install --frozen-lockfile
else
    echo "   Running npm install..."
    npm install
fi
echo "   ✅ Dependencies checked"

# Step 3: Compile TypeScript (if applicable)
echo ""
echo "🔨 Step 3: Compiling TypeScript..."
if [ -f "tsconfig.json" ]; then
    if command -v tsc &> /dev/null; then
        echo "   Running TypeScript compiler..."
        npx tsc --noEmit
        echo "   ✅ TypeScript compilation successful"
    else
        echo "   ⚠️  TypeScript compiler not found, skipping compilation check..."
    fi
else
    echo "   ⚠️  No tsconfig.json found, skipping TypeScript compilation..."
fi

# Step 4: Run tests to verify the fix
echo ""
echo "🧪 Step 4: Testing the timeout fix..."
if [ -f "test-free-user-detection.js" ]; then
    echo "   Running performance tests..."
    echo "   Note: Make sure your API server is running on the configured port"
    echo "   Press Ctrl+C to skip tests if the server is not running"

    # Give user a chance to cancel if server is not running
    sleep 3

    # Run tests with timeout
    timeout 60s node test-free-user-detection.js || {
        echo "   ⚠️  Tests timed out or failed. This might be expected if the server is not running."
        echo "   You can run 'node test-free-user-detection.js' manually later."
    }
else
    echo "   ⚠️  Test script not found, skipping tests..."
fi

# Step 5: Display deployment summary
echo ""
echo "📋 Deployment Summary"
echo "===================="
echo ""
echo "✅ Applied Optimizations:"
echo "   • Performance optimization middleware"
echo "   • Fast path detection for paid users"
echo "   • Enhanced caching in free user limiter"
echo "   • Database indexes for inbox count queries"
echo "   • Request timeout management"
echo "   • Response time monitoring"
echo ""
echo "🎯 Expected Performance Improvements:"
echo "   • Paid users: < 500ms (fast path)"
echo "   • Free users (cache hit): < 100ms"
echo "   • Free users (cache miss): < 2000ms"
echo "   • No more timeout errors (408)"
echo ""
echo "📊 Monitoring Commands:"
echo "   • Check logs: tail -f logs/api.log"
echo "   • Run tests: node test-free-user-detection.js"
echo "   • Monitor performance: grep 'Inbox creation completed' logs/api.log"
echo ""
echo "🔧 Environment Variables (optional):"
echo "   • NODE_ENV=development (for detailed logging)"
echo "   • DEBUG_AUTH=true (for authentication debugging)"
echo ""
echo "🚀 Next Steps:"
echo "   1. Restart your API server to apply the changes"
echo "   2. Run the test script to verify performance"
echo "   3. Monitor response times in production"
echo "   4. Check that free user limits still work correctly"
echo ""

# Coolify-specific deployment instructions
echo "🚀 Coolify Deployment Instructions:"
echo "   1. The code changes are ready for deployment"
echo "   2. Commit and push your changes to trigger Coolify deployment:"
echo "      git add ."
echo "      git commit -m 'Fix: Implement timeout optimizations for POST /inboxes'"
echo "      git push origin main"
echo "   3. Coolify will automatically rebuild and redeploy your application"
echo "   4. Database optimizations will be applied during container startup"

echo ""
echo "✨ Timeout fix deployment completed successfully!"
echo ""
echo "📖 For detailed information, see:"
echo "   • TIMEOUT-FIX-IMPLEMENTATION.md"
echo "   • FREE-USER-DETECTION-IMPLEMENTATION.md"
